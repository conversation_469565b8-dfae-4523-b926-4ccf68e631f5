#!/usr/bin/env python3
"""
依赖库安装脚本
自动安装BOSS直聘爬虫所需的所有依赖库
"""

import subprocess
import sys
import os
import platform

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package):
    """安装单个包"""
    print(f"📦 正在安装 {package}...")
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install {package}")
    
    if success:
        print(f"✅ {package} 安装成功")
        return True
    else:
        print(f"❌ {package} 安装失败: {stderr}")
        return False

def install_playwright_browsers():
    """安装Playwright浏览器"""
    print("🎭 正在安装Playwright浏览器...")
    success, stdout, stderr = run_command("playwright install chromium")
    
    if success:
        print("✅ Playwright浏览器安装成功")
        return True
    else:
        print(f"❌ Playwright浏览器安装失败: {stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def main():
    """主函数"""
    print("🚀 BOSS直聘爬虫依赖安装程序")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 升级pip
    print("📈 升级pip...")
    run_command(f"{sys.executable} -m pip install --upgrade pip")
    
    # 基础依赖
    basic_packages = [
        "beautifulsoup4>=4.12.0",
        "lxml>=4.9.0",
        "aiohttp>=3.8.0",
        "requests>=2.31.0"
    ]
    
    # 先进反爬虫技术库
    advanced_packages = [
        "seleniumbase>=4.20.0",
        "playwright>=1.40.0",
        "cloudscraper>=1.2.71"
    ]
    
    # 可选的高级库（可能安装失败）
    optional_packages = [
        "curl-cffi>=0.5.10",
        "tls-client>=0.2.2",
        "ddddocr>=1.4.11",
        "undetected-chromedriver>=3.5.4"
    ]
    
    # 数据处理库
    data_packages = [
        "pandas>=2.1.0",
        "openpyxl>=3.1.0"
    ]
    
    # 加密解密库
    crypto_packages = [
        "pycryptodome>=3.19.0",
        "cryptography>=41.0.0"
    ]
    
    # 工具库
    utility_packages = [
        "fake-useragent>=1.4.0",
        "python-dotenv>=1.0.0"
    ]
    
    # 安装基础依赖
    print("\n📦 安装基础依赖...")
    for package in basic_packages:
        install_package(package)
    
    # 安装先进反爬虫技术库
    print("\n🛡️ 安装先进反爬虫技术库...")
    for package in advanced_packages:
        install_package(package)
    
    # 安装可选的高级库
    print("\n🔥 安装可选的高级库...")
    for package in optional_packages:
        success = install_package(package)
        if not success:
            print(f"⚠️ {package} 安装失败，但不影响基本功能")
    
    # 安装数据处理库
    print("\n📊 安装数据处理库...")
    for package in data_packages:
        install_package(package)
    
    # 安装加密解密库
    print("\n🔐 安装加密解密库...")
    for package in crypto_packages:
        install_package(package)
    
    # 安装工具库
    print("\n🔧 安装工具库...")
    for package in utility_packages:
        install_package(package)
    
    # 安装Playwright浏览器
    print("\n🎭 安装Playwright浏览器...")
    install_playwright_browsers()
    
    print("\n" + "=" * 60)
    print("🎉 依赖安装完成！")
    print("\n📋 安装总结:")
    print("✅ 基础爬虫功能已就绪")
    print("✅ 先进反爬虫技术已配置")
    print("✅ 数据处理功能已安装")
    print("✅ 加密解密功能已安装")
    
    print("\n🚀 现在可以运行爬虫了:")
    print("   python main.py --quick")
    print("   python main.py --cities 北京 上海")
    print("   python main.py --keywords Python Java")
    
    # 检查安装结果
    print("\n🔍 检查关键库安装状态:")
    test_imports = [
        ("beautifulsoup4", "bs4"),
        ("aiohttp", "aiohttp"),
        ("seleniumbase", "seleniumbase"),
        ("playwright", "playwright"),
        ("cloudscraper", "cloudscraper"),
        ("requests", "requests")
    ]
    
    for package_name, import_name in test_imports:
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name}")

if __name__ == "__main__":
    main()
