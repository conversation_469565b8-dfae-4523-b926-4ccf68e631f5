#!/usr/bin/env python3
"""
BOSS直聘高级爬虫模块
集成GitHub最先进的反爬虫技术和Position_Crawler_1项目的核心逻辑
支持TLS指纹伪造、验证码识别、参数加密等高级功能
"""

import asyncio
import time
import random
import json
import os
import re
import hashlib
import base64
import logging
from typing import List, Dict, Set, Any, Optional
from urllib.parse import urljoin, urlparse, quote
from bs4 import BeautifulSoup
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 检测高级库的可用性
ADVANCED_METHODS = {}

try:
    import curl_cffi
    from curl_cffi import requests as cf_requests
    ADVANCED_METHODS['curl_cffi'] = True
    logger.info("🔥 curl-cffi (TLS指纹伪造) 已加载")
except ImportError:
    ADVANCED_METHODS['curl_cffi'] = False
    logger.warning("⚠️ curl-cffi 未安装，请运行: pip install curl-cffi")

try:
    import tls_client
    ADVANCED_METHODS['tls_client'] = True
    logger.info("🔐 tls-client (浏览器指纹) 已加载")
except ImportError:
    ADVANCED_METHODS['tls_client'] = False
    logger.warning("⚠️ tls-client 未安装，请运行: pip install tls-client")

try:
    import ddddocr
    ADVANCED_METHODS['ddddocr'] = True
    logger.info("🎯 ddddocr (验证码识别) 已加载")
except ImportError:
    ADVANCED_METHODS['ddddocr'] = False
    logger.warning("⚠️ ddddocr 未安装，请运行: pip install ddddocr")

try:
    from fake_useragent import UserAgent
    ADVANCED_METHODS['fake_useragent'] = True
    logger.info("🎭 fake-useragent 已加载")
except ImportError:
    ADVANCED_METHODS['fake_useragent'] = False
    logger.warning("⚠️ fake-useragent 未安装，请运行: pip install fake-useragent")

class BOSSAdvancedCrawler:
    """BOSS直聘高级爬虫 - 集成最先进的反爬虫技术"""
    
    def __init__(self):
        self.base_url = "https://www.zhipin.com"
        self.search_url = "https://www.zhipin.com/web/geek/job"
        self.collected_urls = set()
        
        # 从config导入配置
        from config import CITY_CODES, USER_AGENTS
        self.city_codes = CITY_CODES
        self.user_agents = USER_AGENTS
        
        # 初始化高级工具
        self.ua_generator = None
        self.ocr_engine = None
        self._init_advanced_tools()
        
        # 统计信息
        self.stats = {
            'successful_method': '',
            'pages_crawled': 0,
            'urls_found': 0,
            'unique_urls': 0,
            'failed_attempts': 0,
            'captcha_solved': 0,
            'start_time': None,
            'end_time': None
        }
        
    def _init_advanced_tools(self):
        """初始化高级工具"""
        if ADVANCED_METHODS['fake_useragent']:
            self.ua_generator = UserAgent()
            
        if ADVANCED_METHODS['ddddocr']:
            self.ocr_engine = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
            
    def get_advanced_headers(self) -> Dict[str, str]:
        """获取高级请求头"""
        if self.ua_generator:
            user_agent = self.ua_generator.chrome
        else:
            user_agent = random.choice(self.user_agents)
            
        return {
            "User-Agent": user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
            "Referer": "https://www.zhipin.com/",
            # 添加BOSS直聘特定的请求头
            "X-Requested-With": "XMLHttpRequest",
            "Origin": "https://www.zhipin.com"
        }
        
    def method_curl_cffi_advanced(self, url: str) -> str:
        """使用curl-cffi进行TLS指纹伪造"""
        if not ADVANCED_METHODS['curl_cffi']:
            return ""
            
        try:
            logger.info("🔥 使用curl-cffi方法 (TLS指纹伪造)")
            
            headers = self.get_advanced_headers()
            
            # 预热访问
            cf_requests.get("https://www.zhipin.com", headers=headers, impersonate="chrome120")
            time.sleep(random.uniform(1, 3))
            
            # 访问目标页面
            response = cf_requests.get(url, headers=headers, impersonate="chrome120")
            
            if response.status_code == 200:
                self.stats['successful_method'] = 'curl-cffi-Advanced'
                return response.text
            else:
                logger.warning(f"⚠️ curl-cffi响应状态码: {response.status_code}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ curl-cffi方法失败: {e}")
            return ""
            
    def method_tls_client_advanced(self, url: str) -> str:
        """使用tls-client模拟真实浏览器指纹"""
        if not ADVANCED_METHODS['tls_client']:
            return ""
            
        try:
            logger.info("🔐 使用tls-client方法 (真实浏览器指纹)")
            
            # 创建TLS客户端，模拟Chrome浏览器
            session = tls_client.Session(
                client_identifier="chrome120",
                random_tls_extension_order=True
            )
            
            headers = self.get_advanced_headers()
            
            # 预热访问
            session.get("https://www.zhipin.com", headers=headers)
            time.sleep(random.uniform(1, 3))
            
            # 访问目标页面
            response = session.get(url, headers=headers)
            
            if response.status_code == 200:
                self.stats['successful_method'] = 'tls-client-Advanced'
                return response.text
            else:
                logger.warning(f"⚠️ tls-client响应状态码: {response.status_code}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ tls-client方法失败: {e}")
            return ""
            
    def solve_captcha(self, captcha_image_url: str) -> Optional[str]:
        """解决验证码"""
        if not ADVANCED_METHODS['ddddocr'] or not self.ocr_engine:
            return None
            
        try:
            logger.info("🎯 开始识别验证码")
            
            # 下载验证码图片
            headers = self.get_advanced_headers()
            
            if ADVANCED_METHODS['curl_cffi']:
                response = cf_requests.get(captcha_image_url, headers=headers, impersonate="chrome120")
            else:
                import requests
                response = requests.get(captcha_image_url, headers=headers)
                
            if response.status_code == 200:
                # 识别验证码
                result = self.ocr_engine.classification(response.content)
                self.stats['captcha_solved'] += 1
                logger.info(f"✅ 验证码识别结果: {result}")
                return result
            else:
                logger.error(f"❌ 验证码图片下载失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 验证码识别失败: {e}")
            return None
            
    def generate_boss_signature(self, timestamp: int, params: Dict[str, Any]) -> str:
        """生成BOSS直聘签名参数（基于逆向分析）"""
        try:
            # 基于spider_reverse项目的BOSS直聘逆向分析
            # 这里实现签名生成逻辑
            
            # 1. 参数排序
            sorted_params = sorted(params.items())
            param_string = "&".join([f"{k}={v}" for k, v in sorted_params])
            
            # 2. 添加时间戳和密钥
            secret_key = "your_secret_key_here"  # 需要通过逆向获取
            sign_string = f"{param_string}&timestamp={timestamp}&key={secret_key}"
            
            # 3. MD5加密
            signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
            
            return signature
            
        except Exception as e:
            logger.error(f"❌ 签名生成失败: {e}")
            return ""
            
    def generate_zp_token(self) -> str:
        """生成BOSS直聘zp_token参数"""
        try:
            # 基于逆向分析的token生成逻辑
            timestamp = int(time.time() * 1000)
            random_str = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=16))
            
            # 简化的token生成逻辑（实际需要根据逆向结果调整）
            token_data = f"{timestamp}_{random_str}"
            zp_token = base64.b64encode(token_data.encode()).decode()
            
            return zp_token
            
        except Exception as e:
            logger.error(f"❌ zp_token生成失败: {e}")
            return ""
            
    async def crawl_with_advanced_methods(self, url: str) -> str:
        """使用高级方法爬取"""
        logger.info(f"🚀 开始高级爬取: {url}")
        
        # 按成功率排序的高级方法
        methods = []
        
        if ADVANCED_METHODS['curl_cffi']:
            methods.append(('curl-cffi-Advanced', self.method_curl_cffi_advanced, False))
        if ADVANCED_METHODS['tls_client']:
            methods.append(('tls-client-Advanced', self.method_tls_client_advanced, False))
            
        logger.info(f"📋 可用高级方法数量: {len(methods)}")
        
        for i, (method_name, method_func, is_async) in enumerate(methods, 1):
            try:
                logger.info(f"🔄 尝试高级方法 {i}/{len(methods)}: {method_name}")
                
                if is_async:
                    html_content = await method_func(url)
                else:
                    html_content = method_func(url)
                    
                if html_content and len(html_content) > 5000:
                    # 检查是否包含职位数据
                    if any(keyword in html_content.lower() for keyword in ['job', 'position', '职位', '工作', 'zhipin']):
                        logger.info(f"✅ 高级方法 {i} ({method_name}) 成功获取页面内容")
                        return html_content
                    else:
                        logger.warning(f"⚠️ 高级方法 {i} ({method_name}) 获取页面但无职位相关内容")
                else:
                    logger.warning(f"❌ 高级方法 {i} ({method_name}) 获取页面失败或内容不完整")
                    
            except Exception as e:
                logger.error(f"❌ 高级方法 {i} ({method_name}) 异常: {str(e)}")
                continue
                
            # 方法间延迟
            await asyncio.sleep(random.uniform(1, 3))
            
        logger.error("❌ 所有高级方法都失败了")
        self.stats['failed_attempts'] += 1
        return ""
