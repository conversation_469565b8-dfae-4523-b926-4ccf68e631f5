#!/usr/bin/env python3
"""
BOSS直聘职位URL爬虫主程序
基于Position_Crawler_1项目核心技术
"""

import asyncio
import sys
from datetime import datetime
from boss_url_crawler import BOSSURLCrawler
from config import MAJOR_CITIES, HIGH_PRIORITY_KEYWORDS

async def crawl_boss_urls():
    """爬取BOSS直聘职位URL"""
    print("🚀 BOSS直聘职位URL爬虫")
    print("=" * 50)

    async with BOSSURLCrawler() as crawler:
        # 使用默认配置：主要城市 + 热门关键词
        urls = await crawler.crawl_all_job_urls(
            cities=MAJOR_CITIES[:3],  # 前3个主要城市
            keywords=HIGH_PRIORITY_KEYWORDS[:3],  # 前3个热门关键词
            max_pages_per_search=5  # 每个搜索最多5页
        )

        # 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"boss_urls_{timestamp}.txt"
        filepath = crawler.save_urls_to_file(filename)

        # 打印统计信息
        crawler.print_statistics()

        return len(urls), filepath

async def main():
    """主函数"""
    try:
        url_count, filepath = await crawl_boss_urls()
        print(f"\n🎉 爬取完成！")
        print(f"📊 共获取 {url_count} 个唯一职位URL")
        print(f"💾 结果已保存到: {filepath}")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)

    # 运行主程序
    asyncio.run(main())
