#!/usr/bin/env python3
"""
BOSS直聘职位URL爬虫主程序
直接使用Position_Crawler_1项目的核心技术
"""

import sys
from position_crawler_core import PositionCrawlerCore
from config import MAJOR_CITIES, HIGH_PRIORITY_KEYWORDS

def main():
    """主函数"""
    print("🚀 BOSS直聘职位URL爬虫")
    print("🔥 使用Position_Crawler_1项目核心技术")
    print("=" * 60)

    try:
        # 创建Position_Crawler_1核心爬虫
        crawler = PositionCrawlerCore()

        # 使用Position_Crawler_1技术爬取
        urls = crawler.crawl_boss_jobs(
            cities=MAJOR_CITIES[:3],  # 前3个主要城市
            keywords=HIGH_PRIORITY_KEYWORDS[:3]  # 前3个热门关键词
        )

        # 保存结果
        filepath = crawler.save_results()

        # 打印统计信息
        crawler.print_statistics()

        print(f"\n🎉 Position_Crawler_1技术爬取完成！")
        print(f"📊 共获取 {len(urls)} 个唯一职位URL")
        print(f"💾 结果已保存到: {filepath}")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)

    # 运行主程序
    main()
