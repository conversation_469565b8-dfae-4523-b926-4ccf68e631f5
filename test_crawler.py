#!/usr/bin/env python3
"""
BOSS直聘爬虫测试脚本
验证各种爬虫技术的可用性和效果
"""

import asyncio
import sys
import time
from datetime import datetime

def test_imports():
    """测试所有依赖库的导入"""
    print("🔍 测试依赖库导入...")
    
    test_results = {}
    
    # 基础库
    basic_libs = [
        ("beautifulsoup4", "bs4"),
        ("lxml", "lxml"),
        ("aiohttp", "aiohttp"),
        ("requests", "requests")
    ]
    
    # 先进反爬虫库
    advanced_libs = [
        ("seleniumbase", "seleniumbase"),
        ("playwright", "playwright"),
        ("cloudscraper", "cloudscraper")
    ]
    
    # 可选高级库
    optional_libs = [
        ("curl-cffi", "curl_cffi"),
        ("tls-client", "tls_client"),
        ("ddddocr", "ddddocr"),
        ("undetected-chromedriver", "undetected_chromedriver")
    ]
    
    all_libs = basic_libs + advanced_libs + optional_libs
    
    for lib_name, import_name in all_libs:
        try:
            __import__(import_name)
            test_results[lib_name] = True
            print(f"✅ {lib_name}")
        except ImportError as e:
            test_results[lib_name] = False
            print(f"❌ {lib_name}: {e}")
    
    return test_results

async def test_basic_crawler():
    """测试基础爬虫功能"""
    print("\n🚀 测试基础爬虫功能...")
    
    try:
        from boss_url_crawler import BOSSURLCrawler
        
        async with BOSSURLCrawler() as crawler:
            # 测试单页爬取
            print("📄 测试单页爬取...")
            urls = await crawler.crawl_search_page("101010100", "Python", 1)
            
            if urls:
                print(f"✅ 成功爬取 {len(urls)} 个URL")
                print("📋 示例URL:")
                for i, url in enumerate(urls[:3]):
                    print(f"   {i+1}. {url}")
                return True
            else:
                print("❌ 未能获取到URL")
                return False
                
    except Exception as e:
        print(f"❌ 基础爬虫测试失败: {e}")
        return False

async def test_advanced_crawler():
    """测试高级爬虫功能"""
    print("\n🔥 测试高级爬虫功能...")
    
    try:
        from boss_advanced_crawler import BOSSAdvancedCrawler
        
        crawler = BOSSAdvancedCrawler()
        
        # 测试高级方法
        test_url = "https://www.zhipin.com/web/geek/job?query=Python&city=101010100"
        content = await crawler.crawl_with_advanced_methods(test_url)
        
        if content and len(content) > 1000:
            print(f"✅ 高级爬虫成功获取内容 ({len(content)} 字符)")
            return True
        else:
            print("❌ 高级爬虫未能获取内容")
            return False
            
    except Exception as e:
        print(f"❌ 高级爬虫测试失败: {e}")
        return False

def test_config():
    """测试配置文件"""
    print("\n⚙️ 测试配置文件...")
    
    try:
        from config import CITY_CODES, HIGH_PRIORITY_KEYWORDS, MAJOR_CITIES, USER_AGENTS
        
        print(f"✅ 城市代码数量: {len(CITY_CODES)}")
        print(f"✅ 高优先级关键词数量: {len(HIGH_PRIORITY_KEYWORDS)}")
        print(f"✅ 主要城市数量: {len(MAJOR_CITIES)}")
        print(f"✅ User-Agent数量: {len(USER_AGENTS)}")
        
        # 显示一些示例
        print("📋 示例配置:")
        print(f"   主要城市: {MAJOR_CITIES[:3]}")
        print(f"   热门关键词: {HIGH_PRIORITY_KEYWORDS[:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

async def test_full_workflow():
    """测试完整工作流程"""
    print("\n🎯 测试完整工作流程...")
    
    try:
        from boss_url_crawler import BOSSURLCrawler
        
        async with BOSSURLCrawler() as crawler:
            # 小规模测试
            print("📊 开始小规模爬取测试...")
            urls = await crawler.crawl_all_job_urls(
                cities=["北京"],
                keywords=["Python"],
                max_pages_per_search=1
            )
            
            if urls:
                print(f"✅ 完整流程测试成功，获取 {len(urls)} 个URL")
                
                # 保存测试结果
                filename = f"test_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                filepath = crawler.save_urls_to_file(filename)
                print(f"💾 测试结果已保存: {filepath}")
                
                # 打印统计信息
                crawler.print_statistics()
                
                return True
            else:
                print("❌ 完整流程测试失败，未获取到URL")
                return False
                
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "=" * 80)
    print("📊 测试报告")
    print("=" * 80)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！爬虫系统已就绪")
        print("\n🚀 可以开始使用:")
        print("   python main.py --quick")
        print("   python main.py --cities 北京 上海 --keywords Python Java")
    else:
        print("\n⚠️ 部分测试失败，请检查依赖安装")
        print("💡 建议运行: python install_dependencies.py")

async def main():
    """主测试函数"""
    print("🧪 BOSS直聘爬虫系统测试")
    print("=" * 80)
    
    results = {}
    
    # 1. 测试依赖导入
    import_results = test_imports()
    results['依赖库导入'] = all(import_results.values())
    
    # 2. 测试配置文件
    results['配置文件'] = test_config()
    
    # 3. 测试基础爬虫
    results['基础爬虫'] = await test_basic_crawler()
    
    # 4. 测试高级爬虫
    results['高级爬虫'] = await test_advanced_crawler()
    
    # 5. 测试完整工作流程
    results['完整流程'] = await test_full_workflow()
    
    # 生成测试报告
    generate_test_report(results)

if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 运行测试
    asyncio.run(main())
