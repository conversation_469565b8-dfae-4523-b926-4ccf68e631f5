#!/usr/bin/env python3
"""
快速测试脚本 - 专门测试BOSS直聘爬虫的核心功能
"""

import asyncio
import sys
from datetime import datetime

async def test_simple_crawl():
    """简单爬取测试"""
    print("🚀 开始简单爬取测试...")
    
    try:
        from boss_url_crawler import BOSSURLCrawler
        
        async with BOSSURLCrawler() as crawler:
            print("📄 测试单页爬取...")
            
            # 使用更简单的搜索参数
            urls = await crawler.crawl_search_page("", "Python", 1)
            
            if urls:
                print(f"✅ 成功爬取 {len(urls)} 个URL")
                print("📋 前5个URL:")
                for i, url in enumerate(urls[:5], 1):
                    print(f"   {i}. {url}")
                return True
            else:
                print("❌ 未能获取到URL")
                
                # 尝试不同的方法
                print("🔄 尝试使用TLS-Client方法...")
                from boss_advanced_crawler import BOSSAdvancedCrawler
                
                advanced_crawler = BOSSAdvancedCrawler()
                content = await advanced_crawler.crawl_with_advanced_methods(
                    "https://www.zhipin.com/web/geek/job?query=Python"
                )
                
                if content:
                    print(f"✅ TLS-Client成功获取内容 ({len(content)} 字符)")
                    
                    # 手动提取URL
                    from bs4 import BeautifulSoup
                    import re
                    
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    # 查找所有链接
                    all_links = soup.find_all('a', href=True)
                    job_urls = []
                    
                    for link in all_links:
                        href = link.get('href')
                        if href and 'job_detail' in href:
                            if not href.startswith('http'):
                                href = f"https://www.zhipin.com{href}"
                            job_urls.append(href)
                    
                    if job_urls:
                        print(f"✅ 手动提取到 {len(job_urls)} 个职位URL")
                        print("📋 前5个URL:")
                        for i, url in enumerate(job_urls[:5], 1):
                            print(f"   {i}. {url}")
                        return True
                    else:
                        print("❌ 手动提取也未找到职位URL")
                        
                        # 保存页面内容用于调试
                        debug_file = f"debug_content_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                        with open(debug_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        print(f"🔍 页面内容已保存到: {debug_file}")
                        
                        # 分析页面内容
                        print("🔍 页面内容分析:")
                        print(f"   页面长度: {len(content)} 字符")
                        print(f"   是否包含'job': {'job' in content.lower()}")
                        print(f"   是否包含'职位': {'职位' in content}")
                        print(f"   是否包含'招聘': {'招聘' in content}")
                        
                        # 查找可能的职位相关元素
                        if 'job' in content.lower():
                            job_matches = re.findall(r'job[^"]*', content.lower())
                            print(f"   找到job相关内容: {len(job_matches)} 个")
                            if job_matches:
                                print(f"   示例: {job_matches[:3]}")
                
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_different_methods():
    """测试不同的爬取方法"""
    print("\n🔧 测试不同的爬取方法...")
    
    test_url = "https://www.zhipin.com/web/geek/job?query=Python"
    
    try:
        from boss_url_crawler import BOSSURLCrawler
        
        async with BOSSURLCrawler() as crawler:
            # 测试aiohttp方法
            print("🌐 测试aiohttp方法...")
            content1 = await crawler.method_aiohttp_advanced(test_url)
            print(f"   结果: {len(content1) if content1 else 0} 字符")
            
            # 测试CloudScraper方法
            print("☁️ 测试CloudScraper方法...")
            content2 = crawler.method_cloudscraper_advanced(test_url)
            print(f"   结果: {len(content2) if content2 else 0} 字符")
            
            # 测试SeleniumBase方法
            print("🛡️ 测试SeleniumBase方法...")
            content3 = crawler.method_seleniumbase_uc(test_url)
            print(f"   结果: {len(content3) if content3 else 0} 字符")
            
            # 比较结果
            contents = [content1, content2, content3]
            method_names = ["aiohttp", "CloudScraper", "SeleniumBase"]
            
            for i, (content, method) in enumerate(zip(contents, method_names)):
                if content and len(content) > 1000:
                    print(f"✅ {method} 方法成功")
                    
                    # 尝试提取URL
                    urls = crawler.extract_job_urls_from_html(content)
                    print(f"   提取到 {len(urls)} 个URL")
                    
                    if urls:
                        print(f"   示例URL: {urls[0]}")
                        return True
                else:
                    print(f"❌ {method} 方法失败")
            
            return False
            
    except Exception as e:
        print(f"❌ 方法测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🧪 BOSS直聘爬虫快速测试")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return
    
    results = {}
    
    # 测试1: 简单爬取
    results['简单爬取'] = await test_simple_crawl()
    
    # 测试2: 不同方法
    results['不同方法'] = await test_different_methods()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    
    print(f"\n成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count > 0:
        print("\n🎉 部分功能正常，可以尝试运行完整爬虫")
        print("💡 建议运行: python main.py --quick")
    else:
        print("\n⚠️ 所有测试失败，需要进一步调试")
        print("💡 建议检查网络连接和依赖安装")

if __name__ == "__main__":
    asyncio.run(main())
