# 🚀 BOSS直聘职位URL爬虫

基于Position_Crawler_1项目核心技术的专业爬虫程序，专门解决BOSS直聘的反爬虫检测问题。

## ⚡ 快速使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行爬虫
```bash
python main.py
```

### 3. 查看结果
结果保存在 `output/` 目录的txt文件中，每行一个职位URL。

## 📁 项目文件

- `main.py` - 主程序入口
- `position_crawler_core.py` - Position_Crawler_1核心技术
- `config.py` - 配置文件（城市、关键词）
- `requirements.txt` - 依赖库
- `output/` - 输出目录

## 🔧 核心技术

基于Position_Crawler_1项目的核心逻辑：
- ✅ SeleniumBase隐蔽模式
- ✅ 智能延迟策略
- ✅ 人类行为模拟
- ✅ 多层URL提取算法
- ✅ 反检测技术

## ⚠️ 重要说明

**BOSS直聘反爬虫机制说明：**
- BOSS直聘具有严格的反爬虫检测
- 会出现"IP异常行为"和"访客身份验证"
- Position_Crawler_1项目已解决这些核心问题
- 建议直接使用Position_Crawler_1项目的完整解决方案

**使用建议：**
- 合理控制爬取频率
- 避免高峰时段运行
- 如遇验证码，说明需要Position_Crawler_1的完整技术栈
