# BOSS直聘职位URL爬虫

基于Position_Crawler_1项目核心技术的BOSS直聘职位URL爬虫程序。

## 功能特点

- 🚀 异步高性能爬取
- 🛡️ 多种反爬虫技术（aiohttp、CloudScraper、SeleniumBase、Playwright）
- 🎯 智能去重和URL验证
- 📊 详细统计信息
- 💾 自动保存结果到txt文件

## 快速使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
playwright install chromium
```

### 2. 运行爬虫
```bash
python main.py
```

### 3. 查看结果
爬取完成后，结果会保存在 `output/` 目录下的txt文件中。

## 文件说明

- `main.py` - 主程序入口
- `boss_url_crawler.py` - 核心爬虫模块
- `config.py` - 配置文件
- `requirements.txt` - 依赖库列表
- `output/` - 输出目录

## 配置说明

可在 `config.py` 中修改：
- 目标城市列表
- 搜索关键词
- 爬取页数限制

## 注意事项

- 请合理控制爬取频率，避免对目标网站造成压力
- 建议在非高峰时段运行
- 如遇到反爬虫限制，程序会自动切换技术方案
