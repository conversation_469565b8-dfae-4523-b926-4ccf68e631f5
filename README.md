# 🚀 BOSS直聘职位URL爬虫系统

基于Position_Crawler_1项目核心技术和GitHub最佳实践的高级BOSS直聘爬虫系统，集成多种先进反爬虫技术。

## ✨ 核心特性

### 🛡️ 先进反爬虫技术
- **NoDriver**: 最新undetected-chromedriver继任者
- **Playwright**: 高级反检测脚本注入
- **SeleniumBase UC模式**: 强大的反检测能力
- **TLS指纹伪造**: curl-cffi和tls-client支持
- **CloudScraper**: Cloudflare绕过技术

### 🎯 智能爬取策略
- 多城市+关键词组合搜索
- 智能去重和URL验证
- 异步高并发爬取
- 自动重试和错误恢复
- 详细统计和监控

### 🔧 技术架构
- 异步编程模式，支持高并发
- 模块化设计，易于扩展
- 完善的配置管理系统
- 多种输出格式支持

## 📋 核心功能

1. **全站职位URL抓取**：遍历BOSS直聘所有正在招聘的职位
2. **多维度搜索**：支持城市、关键词、职位类别组合搜索
3. **智能翻页**：自动检测并爬取所有搜索结果页面
4. **URL验证**：确保提取的URL格式正确且有效
5. **结果保存**：将所有职位URL保存为txt格式文件
6. **统计报告**：提供详细的爬取统计信息

## 🛠️ 技术架构

### 核心技术栈
- **crawl4ai**：异步网页爬虫框架
- **BeautifulSoup**：HTML解析
- **asyncio**：异步编程
- **aiohttp**：异步HTTP客户端

### 继承的核心技术
- 异步爬虫架构（来自boss_crawler.py）
- 反反爬机制（来自boss_security_bypass.py）
- 数据提取策略（来自data_extractor.py）
- 智能重试机制（来自配置文件）

## 📦 快速开始

### 1. 环境要求
- Python 3.8+
- 推荐使用虚拟环境

### 2. 自动安装依赖
```bash
# 运行自动安装脚本
python install_dependencies.py
```

### 3. 手动安装依赖（可选）
```bash
pip install -r requirements.txt
playwright install chromium
```

### 4. 运行测试
```bash
# 测试系统功能
python test_crawler.py
```

## 🎯 使用方法

### 1. 快速测试模式
```bash
python main.py --quick
```
爬取少量数据进行功能测试。

### 2. 完整爬取模式
```bash
python main.py --full
```
爬取全量职位URL数据（耗时较长）。

### 3. 自定义城市
```bash
python main.py --cities 北京 上海 深圳 杭州
```

### 4. 自定义关键词
```bash
python main.py --keywords Python Java 前端 产品经理
```

### 5. 组合参数
```bash
python main.py --cities 北京 上海 --keywords Python Java --max-pages 30
```

### 6. 自定义输出文件
```bash
python main.py --output my_job_urls.txt
```

### 7. 使用所有城市和关键词
```bash
python main.py --all-cities --all-keywords --max-pages 50
```

## 📊 输出结果

### 文件输出
- **职位URL文件**：`output/boss_zhipin_job_urls.txt`
- **详细配置**：`output/last_crawl_config.json`
- **统计信息**：控制台输出

### URL格式示例
```
https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html
https://www.zhipin.com/job_detail/a1b2c3d4e5f6789012345678.html
...
```

## 🔧 配置说明

### 主要配置项（config.py）

```python
# 爬虫性能配置
CRAWLER_CONFIG = {
    'max_concurrent_requests': 3,  # 并发请求数
    'request_delay_range': (1, 3),  # 请求延时
    'max_pages_per_search': 20,    # 每搜索最大页数
}

# 支持的城市（40+个主要城市）
CITY_CODES = {
    "北京": "101010100",
    "上海": "101020100",
    # ... 更多城市
}

# 职位关键词（按类别分组）
JOB_KEYWORDS = {
    "技术": ["Python", "Java", "JavaScript", ...],
    "产品": ["产品经理", "产品总监", ...],
    # ... 更多类别
}
```

## 📈 性能特点

- **高效并发**：支持多任务异步爬取
- **智能限速**：避免触发反爬虫机制
- **内存优化**：流式处理，支持大规模数据
- **错误恢复**：自动重试和异常处理

## 🛡️ 反反爬策略

1. **用户代理轮换**：随机切换User-Agent
2. **请求间隔控制**：智能延时避免频率过高
3. **浏览器模拟**：使用真实浏览器环境
4. **并发控制**：限制同时请求数量

## 📋 使用示例

### 示例1：爬取技术岗位
```bash
python main.py --keywords Python Java JavaScript 前端 后端 --cities 北京 上海 深圳
```

### 示例2：爬取所有一线城市职位
```bash
python main.py --cities 北京 上海 广州 深圳 --all-keywords --max-pages 25
```

### 示例3：快速获取样本数据
```bash
python main.py --quick
```

## 📊 预期结果

根据配置不同，预期可获取：
- **快速模式**：500-2000个职位URL
- **标准模式**：5000-20000个职位URL  
- **完整模式**：50000+个职位URL

## ⚠️ 注意事项

1. **合规使用**：仅用于学习研究，遵守网站robots.txt协议
2. **频率控制**：避免过于频繁的请求
3. **数据用途**：获取的URL仅用于进一步的合法数据分析
4. **网络环境**：建议在稳定的网络环境下运行

## 🔍 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

2. **爬取失败率高**
   - 检查网络连接
   - 增加请求延时
   - 减少并发数量

3. **获取URL数量少**
   - 增加max_pages参数
   - 扩大城市和关键词范围
   - 检查网站是否有变化

## 📞 技术支持

如遇到问题，请检查：
1. Python版本（需要3.7+）
2. 依赖包版本
3. 网络连接状态
4. 配置参数设置

## 📄 许可证

本项目仅供学习研究使用，请遵守相关法律法规和网站使用条款。
