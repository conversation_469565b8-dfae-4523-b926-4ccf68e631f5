#!/usr/bin/env python3
"""
Position_Crawler_1项目核心逻辑实现
专门解决BOSS直聘反爬虫检测的核心技术
"""

import time
import random
import re
import logging
from typing import List, Set
from urllib.parse import urljoin
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class PositionCrawlerCore:
    """Position_Crawler_1项目的核心爬虫逻辑"""
    
    def __init__(self):
        self.base_url = "https://www.zhipin.com"
        self.collected_urls = set()
        
        # Position_Crawler_1的核心配置
        self.config = {
            'delay_min': 5,
            'delay_max': 10,
            'max_pages': 10,
            'use_stealth_mode': True,
            'simulate_human': True
        }
        
        # 统计信息
        self.stats = {
            'pages_crawled': 0,
            'urls_found': 0,
            'unique_urls': 0,
            'method_used': 'Position_Crawler_Core'
        }
        
    def get_stealth_headers(self) -> dict:
        """获取隐蔽性请求头"""
        return {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0"
        }
        
    def use_seleniumbase_stealth(self, search_params: dict) -> List[str]:
        """使用SeleniumBase隐蔽模式 - Position_Crawler_1的核心方法"""
        try:
            from seleniumbase import SB
            
            logger.info("🛡️ 启动Position_Crawler_1核心技术 (SeleniumBase隐蔽模式)")
            
            # 构造搜索URL
            url = self._build_search_url(search_params)
            logger.info(f"🔍 目标URL: {url}")
            
            with SB(uc=True, test=True, headless=True, 
                   disable_csp=True, disable_ws=True, 
                   block_images=True, do_not_track=True) as sb:
                
                # 第一步：访问首页建立会话
                logger.info("📱 第一步：建立会话...")
                sb.open("https://www.zhipin.com")
                sb.sleep(random.uniform(3, 6))
                
                # 第二步：模拟人类浏览行为
                logger.info("🤖 第二步：模拟人类行为...")
                sb.scroll_to_bottom()
                sb.sleep(random.uniform(1, 3))
                sb.scroll_to_top()
                sb.sleep(random.uniform(2, 4))
                
                # 第三步：访问搜索页面
                logger.info("🎯 第三步：访问搜索页面...")
                sb.open(url)
                sb.sleep(random.uniform(5, 8))
                
                # 第四步：等待页面加载
                logger.info("⏳ 第四步：等待页面加载...")
                try:
                    # 等待职位列表加载
                    sb.wait_for_element_visible("li", timeout=15)
                    sb.sleep(random.uniform(2, 4))
                except:
                    logger.warning("⚠️ 未检测到标准职位列表，继续尝试...")
                
                # 第五步：模拟滚动浏览
                logger.info("📜 第五步：模拟滚动浏览...")
                for i in range(3):
                    sb.scroll_to_bottom()
                    sb.sleep(random.uniform(1, 2))
                    sb.scroll_to_top()
                    sb.sleep(random.uniform(1, 2))
                
                # 第六步：获取页面内容
                logger.info("📄 第六步：获取页面内容...")
                html_content = sb.get_page_source()
                
                # 第七步：提取职位URL
                logger.info("🔗 第七步：提取职位URL...")
                urls = self._extract_urls_from_html(html_content)
                
                if urls:
                    logger.info(f"✅ Position_Crawler_1技术成功提取 {len(urls)} 个URL")
                    self.stats['pages_crawled'] += 1
                    self.stats['urls_found'] += len(urls)
                    return urls
                else:
                    logger.warning("⚠️ 未提取到URL，保存页面用于分析")
                    self._save_debug_page(html_content)
                    return []
                    
        except ImportError:
            logger.error("❌ SeleniumBase未安装，无法使用Position_Crawler_1核心技术")
            return []
        except Exception as e:
            logger.error(f"❌ Position_Crawler_1技术执行失败: {e}")
            return []
            
    def _build_search_url(self, params: dict) -> str:
        """构造搜索URL"""
        base_url = "https://www.zhipin.com/web/geek/job"
        query_parts = []
        
        if params.get('keyword'):
            query_parts.append(f"query={params['keyword']}")
        if params.get('city_code'):
            query_parts.append(f"city={params['city_code']}")
        if params.get('page', 1) > 1:
            query_parts.append(f"page={params['page']}")
            
        if query_parts:
            return f"{base_url}?" + "&".join(query_parts)
        return base_url
        
    def _extract_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取职位URL - Position_Crawler_1优化版"""
        soup = BeautifulSoup(html_content, 'html.parser')
        urls = []
        
        logger.info("🔍 使用Position_Crawler_1的URL提取算法...")
        
        # Position_Crawler_1的多层提取策略
        
        # 策略1: 查找所有包含job_detail的链接
        all_links = soup.find_all('a', href=True)
        for link in all_links:
            href = link.get('href')
            if href and 'job_detail' in href:
                full_url = urljoin(self.base_url, href)
                if self._is_valid_job_url(full_url):
                    urls.append(full_url)
        
        # 策略2: 正则表达式提取
        patterns = [
            r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9_-]+\.html',
            r'/job_detail/[a-zA-Z0-9_-]+\.html',
            r'job_detail/[a-zA-Z0-9_-]+\.html'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if not match.startswith('http'):
                    match = urljoin(self.base_url, match)
                if self._is_valid_job_url(match):
                    urls.append(match)
        
        # 策略3: 从JavaScript中提取
        js_patterns = [
            r'"encryptJobId":\s*"([^"]+)"',
            r'"jobId":\s*"([^"]+)"',
            r'"lid":\s*"([^"]+)"'
        ]
        
        for pattern in js_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                url = f"https://www.zhipin.com/job_detail/{match}.html"
                if self._is_valid_job_url(url):
                    urls.append(url)
        
        # 去重
        unique_urls = list(set(urls))
        logger.info(f"📊 Position_Crawler_1算法提取结果: {len(unique_urls)} 个唯一URL")
        
        return unique_urls
        
    def _is_valid_job_url(self, url: str) -> bool:
        """验证职位URL有效性"""
        if not url or not isinstance(url, str):
            return False
            
        patterns = [
            r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9_-]+\.html',
            r'https://www\.zhipin\.com/position_detail/[a-zA-Z0-9_-]+\.html'
        ]
        
        for pattern in patterns:
            if re.match(pattern, url):
                return True
        return False
        
    def _save_debug_page(self, html_content: str):
        """保存调试页面"""
        timestamp = int(time.time())
        filename = f"position_crawler_debug_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"🔍 调试页面已保存: {filename}")
        
        # 简单分析
        soup = BeautifulSoup(html_content, 'html.parser')
        logger.info(f"📊 页面分析: 长度={len(html_content)}, 链接数={len(soup.find_all('a'))}")
        
    def crawl_boss_jobs(self, cities: List[str] = None, keywords: List[str] = None) -> Set[str]:
        """使用Position_Crawler_1技术爬取BOSS直聘职位"""
        logger.info("🚀 启动Position_Crawler_1核心爬虫技术")
        
        if not cities:
            cities = ["北京", "上海", "深圳"]
        if not keywords:
            keywords = ["Python", "Java", "前端"]
            
        # 城市代码映射
        city_codes = {
            "北京": "101010100",
            "上海": "101020100", 
            "深圳": "101280600",
            "杭州": "101210100",
            "广州": "101280100"
        }
        
        all_urls = set()
        
        # 核心爬取逻辑
        for city in cities:
            city_code = city_codes.get(city, "")
            for keyword in keywords:
                logger.info(f"🎯 爬取: {city} + {keyword}")
                
                search_params = {
                    'city_code': city_code,
                    'keyword': keyword,
                    'page': 1
                }
                
                # 使用Position_Crawler_1核心技术
                urls = self.use_seleniumbase_stealth(search_params)
                all_urls.update(urls)
                
                logger.info(f"📈 当前累计URL数: {len(all_urls)}")
                
                # Position_Crawler_1的关键延迟策略
                delay = random.uniform(self.config['delay_min'], self.config['delay_max'])
                logger.info(f"⏱️ Position_Crawler_1延迟策略: {delay:.1f}秒")
                time.sleep(delay)
        
        self.stats['unique_urls'] = len(all_urls)
        self.collected_urls = all_urls
        
        logger.info(f"🎉 Position_Crawler_1技术完成，共获取 {len(all_urls)} 个职位URL")
        
        return all_urls
        
    def save_results(self, filename: str = None) -> str:
        """保存结果"""
        if not filename:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"position_crawler_urls_{timestamp}.txt"
            
        os.makedirs("output", exist_ok=True)
        filepath = os.path.join("output", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Position_Crawler_1技术爬取结果\n")
            f.write(f"# 爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 使用技术: {self.stats['method_used']}\n")
            f.write(f"# 总计URL数: {len(self.collected_urls)}\n")
            f.write(f"# ==========================================\n\n")
            
            for url in sorted(self.collected_urls):
                f.write(url + '\n')
                
        logger.info(f"💾 结果已保存到: {filepath}")
        return filepath
        
    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "=" * 80)
        print("📊 Position_Crawler_1技术统计报告")
        print("=" * 80)
        print(f"🏆 使用技术: {self.stats['method_used']}")
        print(f"📄 爬取页面: {self.stats['pages_crawled']}")
        print(f"🔗 发现URL总数: {self.stats['urls_found']}")
        print(f"✨ 去重后URL数: {self.stats['unique_urls']}")
        print("=" * 80)
