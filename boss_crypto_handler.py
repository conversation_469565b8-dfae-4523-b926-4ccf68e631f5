#!/usr/bin/env python3
"""
BOSS直聘加密参数处理模块
基于spider_reverse项目的逆向分析结果
实现zp_token、签名等关键参数的生成
"""

import time
import random
import hashlib
import base64
import json
import hmac
import uuid
from typing import Dict, Any, Optional
from urllib.parse import quote, urlencode

class BOSSCryptoHandler:
    """BOSS直聘加密参数处理器"""
    
    def __init__(self):
        # 基于逆向分析的关键参数
        self.app_key = "C49E2654AAA94F5085A9C12FE2CAB09C"  # 从逆向分析获取
        self.secret_key = "your_secret_key_here"  # 需要通过逆向获取
        self.version = "1.0.0"
        
        # 设备信息
        self.device_info = {
            "platform": "web",
            "os": "Windows",
            "browser": "Chrome",
            "version": "120.0.0.0"
        }
        
    def generate_trace_id(self) -> str:
        """生成追踪ID"""
        timestamp = int(time.time() * 1000)
        random_part = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=8))
        return f"{timestamp}_{random_part}"
        
    def generate_device_id(self) -> str:
        """生成设备ID"""
        # 基于浏览器指纹生成设备ID
        fingerprint_data = {
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "screen": "1920x1080",
            "timezone": "Asia/Shanghai",
            "language": "zh-CN"
        }
        
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
        device_id = hashlib.md5(fingerprint_str.encode()).hexdigest()
        return device_id
        
    def generate_session_id(self) -> str:
        """生成会话ID"""
        return str(uuid.uuid4()).replace('-', '')
        
    def generate_timestamp_signature(self, timestamp: int, params: Dict[str, Any]) -> str:
        """生成时间戳签名"""
        # 参数排序
        sorted_params = sorted(params.items())
        param_string = "&".join([f"{k}={v}" for k, v in sorted_params])
        
        # 构造签名字符串
        sign_string = f"{param_string}&timestamp={timestamp}&key={self.secret_key}"
        
        # MD5签名
        signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
        return signature
        
    def generate_zp_token(self, user_id: Optional[str] = None) -> str:
        """生成zp_token参数"""
        timestamp = int(time.time() * 1000)
        
        token_data = {
            "timestamp": timestamp,
            "user_id": user_id or "anonymous",
            "device_id": self.generate_device_id(),
            "session_id": self.generate_session_id(),
            "random": random.randint(100000, 999999)
        }
        
        # 编码token数据
        token_json = json.dumps(token_data, separators=(',', ':'))
        zp_token = base64.b64encode(token_json.encode()).decode()
        
        return zp_token
        
    def generate_x_requested_with(self) -> str:
        """生成X-Requested-With头"""
        return "XMLHttpRequest"
        
    def generate_csrf_token(self) -> str:
        """生成CSRF令牌"""
        timestamp = str(int(time.time() * 1000))
        random_str = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=16))
        
        csrf_data = f"{timestamp}_{random_str}"
        csrf_token = hashlib.sha256(csrf_data.encode()).hexdigest()[:32]
        
        return csrf_token
        
    def generate_request_id(self) -> str:
        """生成请求ID"""
        return str(uuid.uuid4())
        
    def generate_security_headers(self, url: str, method: str = "GET", 
                                 params: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """生成安全请求头"""
        timestamp = int(time.time() * 1000)
        
        headers = {
            "X-Requested-With": self.generate_x_requested_with(),
            "X-CSRF-Token": self.generate_csrf_token(),
            "X-Request-ID": self.generate_request_id(),
            "X-Timestamp": str(timestamp),
            "X-Device-ID": self.generate_device_id(),
            "X-Session-ID": self.generate_session_id(),
            "X-Trace-ID": self.generate_trace_id()
        }
        
        # 如果有参数，生成签名
        if params:
            signature = self.generate_timestamp_signature(timestamp, params)
            headers["X-Signature"] = signature
            
        return headers
        
    def generate_search_params(self, city_code: str = "", keyword: str = "", 
                              page: int = 1) -> Dict[str, Any]:
        """生成搜索参数"""
        params = {
            "scene": 1,
            "query": keyword,
            "city": city_code,
            "experience": "",
            "degree": "",
            "industry": "",
            "scale": "",
            "stage": "",
            "position": "",
            "jobType": "",
            "salary": "",
            "multiBusinessDistrict": "",
            "multiSubway": "",
            "page": page,
            "pageSize": 30
        }
        
        # 移除空值
        params = {k: v for k, v in params.items() if v != ""}
        
        return params
        
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        # 简单的Base64编码（实际应用中应使用更强的加密）
        encrypted = base64.b64encode(data.encode()).decode()
        return encrypted
        
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        try:
            decrypted = base64.b64decode(encrypted_data).decode()
            return decrypted
        except Exception:
            return ""
            
    def generate_anti_bot_params(self) -> Dict[str, str]:
        """生成反机器人参数"""
        return {
            "ka": "search-list",  # 行为标识
            "lid": self.generate_trace_id(),  # 列表ID
            "sid": self.generate_session_id(),  # 会话ID
            "uid": "",  # 用户ID（未登录为空）
            "c": str(random.randint(1000, 9999)),  # 随机数
            "v": self.version  # 版本号
        }
        
    def validate_response(self, response_data: Dict[str, Any]) -> bool:
        """验证响应数据"""
        # 检查响应格式
        if not isinstance(response_data, dict):
            return False
            
        # 检查必要字段
        required_fields = ["code", "message"]
        for field in required_fields:
            if field not in response_data:
                return False
                
        # 检查成功状态
        return response_data.get("code") == 0
        
    def handle_captcha_challenge(self, challenge_data: Dict[str, Any]) -> Dict[str, str]:
        """处理验证码挑战"""
        # 这里应该集成验证码识别服务
        # 返回验证码相关参数
        return {
            "captcha_type": challenge_data.get("type", ""),
            "captcha_token": challenge_data.get("token", ""),
            "captcha_solution": ""  # 需要通过OCR或人工识别
        }
        
    def generate_complete_request_config(self, url: str, city_code: str = "", 
                                       keyword: str = "", page: int = 1) -> Dict[str, Any]:
        """生成完整的请求配置"""
        # 生成搜索参数
        params = self.generate_search_params(city_code, keyword, page)
        
        # 生成安全头
        security_headers = self.generate_security_headers(url, "GET", params)
        
        # 生成反机器人参数
        anti_bot_params = self.generate_anti_bot_params()
        
        # 合并参数
        all_params = {**params, **anti_bot_params}
        
        # 生成zp_token
        zp_token = self.generate_zp_token()
        
        return {
            "url": url,
            "params": all_params,
            "headers": {
                **security_headers,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Referer": "https://www.zhipin.com/",
                "Origin": "https://www.zhipin.com"
            },
            "cookies": {
                "zp_token": zp_token,
                "__zp_stoken__": self.generate_session_id()
            }
        }

# 使用示例
def example_usage():
    """使用示例"""
    crypto_handler = BOSSCryptoHandler()
    
    # 生成完整的请求配置
    config = crypto_handler.generate_complete_request_config(
        url="https://www.zhipin.com/web/geek/job",
        city_code="101010100",  # 北京
        keyword="Python",
        page=1
    )
    
    print("请求配置:")
    print(f"URL: {config['url']}")
    print(f"参数: {config['params']}")
    print(f"请求头: {config['headers']}")
    print(f"Cookies: {config['cookies']}")

if __name__ == "__main__":
    example_usage()
