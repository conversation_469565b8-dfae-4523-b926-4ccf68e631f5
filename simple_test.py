#!/usr/bin/env python3
"""
简单测试脚本 - 使用已验证可用的方法
"""

import asyncio
import aiohttp
import random
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin

async def test_aiohttp_method():
    """测试aiohttp方法"""
    print("🌐 测试aiohttp方法...")
    
    url = "https://www.zhipin.com/web/geek/job?query=Python"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
        "Referer": "https://www.zhipin.com/"
    }
    
    try:
        connector = aiohttp.TCPConnector(ssl=False, limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # 预热访问
            async with session.get("https://www.zhipin.com", headers=headers) as response:
                await response.text()
            
            await asyncio.sleep(2)
            
            # 访问目标页面
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    content = await response.text()
                    print(f"✅ aiohttp成功获取内容 ({len(content)} 字符)")
                    return content
                else:
                    print(f"❌ aiohttp响应状态码: {response.status}")
                    return ""
                    
    except Exception as e:
        print(f"❌ aiohttp方法失败: {e}")
        return ""

def test_tls_client_method():
    """测试tls-client方法"""
    print("🔐 测试tls-client方法...")
    
    try:
        import tls_client
        
        url = "https://www.zhipin.com/web/geek/job?query=Python"
        
        session = tls_client.Session(
            client_identifier="chrome120",
            random_tls_extension_order=True
        )
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://www.zhipin.com/",
            "Origin": "https://www.zhipin.com"
        }
        
        # 预热访问
        session.get("https://www.zhipin.com", headers=headers)
        
        # 访问目标页面
        response = session.get(url, headers=headers)
        
        if response.status_code == 200:
            print(f"✅ tls-client成功获取内容 ({len(response.text)} 字符)")
            return response.text
        else:
            print(f"❌ tls-client响应状态码: {response.status_code}")
            return ""
            
    except ImportError:
        print("❌ tls-client未安装")
        return ""
    except Exception as e:
        print(f"❌ tls-client方法失败: {e}")
        return ""

def extract_urls_from_content(content):
    """从内容中提取URL"""
    print("🔍 分析页面内容...")
    
    # 基本信息
    print(f"   页面长度: {len(content)} 字符")
    print(f"   包含'job': {'job' in content.lower()}")
    print(f"   包含'职位': {'职位' in content}")
    print(f"   包含'招聘': {'招聘' in content}")
    
    # 使用BeautifulSoup解析
    soup = BeautifulSoup(content, 'html.parser')
    
    # 查找所有链接
    all_links = soup.find_all('a', href=True)
    print(f"   总链接数: {len(all_links)}")
    
    job_urls = []
    
    # 方法1: 直接查找job_detail链接
    for link in all_links:
        href = link.get('href')
        if href and 'job_detail' in href:
            if not href.startswith('http'):
                href = urljoin("https://www.zhipin.com", href)
            job_urls.append(href)
    
    print(f"   方法1找到职位URL: {len(job_urls)}")
    
    # 方法2: 正则表达式搜索
    url_patterns = [
        r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9_-]+\.html[^"\s]*',
        r'/job_detail/[a-zA-Z0-9_-]+\.html[^"\s]*',
        r'"job_detail/[a-zA-Z0-9_-]+\.html[^"]*"',
        r"'job_detail/[a-zA-Z0-9_-]+\.html[^']*'",
        r'job_detail/[a-zA-Z0-9_-]+\.html'
    ]
    
    regex_urls = []
    for pattern in url_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            match = match.strip('"\'')
            if not match.startswith('http'):
                match = urljoin("https://www.zhipin.com", match)
            regex_urls.append(match)
    
    print(f"   方法2找到职位URL: {len(regex_urls)}")
    
    # 合并去重
    all_job_urls = list(set(job_urls + regex_urls))
    print(f"   合并去重后: {len(all_job_urls)}")
    
    # 方法3: 查找JavaScript中的URL
    js_patterns = [
        r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
        r'window\.g_initialProps\s*=\s*({.+?});',
        r'jobList["\']?\s*:\s*(\[.+?\])',
        r'jobs["\']?\s*:\s*(\[.+?\])',
        r'"jobList"\s*:\s*(\[.+?\])',
        r'"data"\s*:\s*({.*?"job_detail".*?})'
    ]
    
    js_urls = []
    for pattern in js_patterns:
        matches = re.findall(pattern, content, re.DOTALL)
        for match in matches:
            job_detail_matches = re.findall(r'/job_detail/[a-zA-Z0-9_-]+\.html', match)
            for url_match in job_detail_matches:
                full_url = urljoin("https://www.zhipin.com", url_match)
                js_urls.append(full_url)
    
    print(f"   方法3(JS)找到职位URL: {len(js_urls)}")
    
    # 最终合并
    final_urls = list(set(all_job_urls + js_urls))
    print(f"   最终职位URL数: {len(final_urls)}")
    
    return final_urls

async def main():
    """主函数"""
    print("🧪 BOSS直聘爬虫简单测试")
    print("=" * 50)
    
    # 测试aiohttp方法
    content1 = await test_aiohttp_method()
    
    if content1:
        urls1 = extract_urls_from_content(content1)
        if urls1:
            print(f"✅ aiohttp方法成功提取 {len(urls1)} 个URL")
            print("📋 前3个URL:")
            for i, url in enumerate(urls1[:3], 1):
                print(f"   {i}. {url}")
        else:
            print("❌ aiohttp方法未提取到URL")
            # 保存调试文件
            with open("debug_aiohttp.html", "w", encoding="utf-8") as f:
                f.write(content1)
            print("🔍 调试文件已保存: debug_aiohttp.html")
    
    print("\n" + "-" * 50)
    
    # 测试tls-client方法
    content2 = test_tls_client_method()
    
    if content2:
        urls2 = extract_urls_from_content(content2)
        if urls2:
            print(f"✅ tls-client方法成功提取 {len(urls2)} 个URL")
            print("📋 前3个URL:")
            for i, url in enumerate(urls2[:3], 1):
                print(f"   {i}. {url}")
        else:
            print("❌ tls-client方法未提取到URL")
            # 保存调试文件
            with open("debug_tls_client.html", "w", encoding="utf-8") as f:
                f.write(content2)
            print("🔍 调试文件已保存: debug_tls_client.html")
    
    print("\n" + "=" * 50)
    print("📊 测试总结")
    
    if content1 or content2:
        print("✅ 至少一种方法能够获取页面内容")
        if (content1 and extract_urls_from_content(content1)) or (content2 and extract_urls_from_content(content2)):
            print("✅ 能够提取职位URL，爬虫基本功能正常")
            print("💡 可以运行完整爬虫: python main.py --quick")
        else:
            print("⚠️ 能获取页面但无法提取URL，可能需要处理JavaScript渲染")
            print("💡 建议使用SeleniumBase或Playwright方法")
    else:
        print("❌ 所有方法都无法获取页面内容")
        print("💡 建议检查网络连接或使用代理")

if __name__ == "__main__":
    asyncio.run(main())
