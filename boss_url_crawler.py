#!/usr/bin/env python3
"""
BOSS直聘URL爬虫核心模块
整合Position_Crawler_1项目的核心技术和GitHub最佳实践
支持多种先进反爬虫技术的智能切换
"""

import asyncio
import time
import random
import json
import os
import re
import logging
from typing import List, Dict, Set, Any, Optional
from urllib.parse import urljoin, urlparse, quote
from bs4 import BeautifulSoup
import aiohttp
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 检测可用的先进爬虫库
AVAILABLE_METHODS = {}

try:
    import requests
    AVAILABLE_METHODS['requests'] = True
    logger.info("🌐 Requests 已加载")
except ImportError:
    AVAILABLE_METHODS['requests'] = False

try:
    import cloudscraper
    AVAILABLE_METHODS['cloudscraper'] = True
    logger.info("☁️ CloudScraper 已加载")
except ImportError:
    AVAILABLE_METHODS['cloudscraper'] = False

try:
    from seleniumbase import SB
    AVAILABLE_METHODS['seleniumbase'] = True
    logger.info("🛡️ SeleniumBase 已加载")
except ImportError:
    AVAILABLE_METHODS['seleniumbase'] = False

try:
    from playwright.async_api import async_playwright
    AVAILABLE_METHODS['playwright'] = True
    logger.info("🎭 Playwright 已加载")
except ImportError:
    AVAILABLE_METHODS['playwright'] = False

class BOSSURLCrawler:
    """BOSS直聘URL爬虫 - 整合多种先进技术"""
    
    def __init__(self):
        self.base_url = "https://www.zhipin.com"
        self.search_url = "https://www.zhipin.com/web/geek/job"
        self.collected_urls = set()
        
        # 从config导入配置
        from config import CITY_CODES, USER_AGENTS, CHROME_OPTIONS, SELECTORS
        self.city_codes = CITY_CODES
        self.user_agents = USER_AGENTS
        self.chrome_options = CHROME_OPTIONS
        self.selectors = SELECTORS
        
        # 统计信息
        self.stats = {
            'successful_method': '',
            'pages_crawled': 0,
            'urls_found': 0,
            'unique_urls': 0,
            'failed_attempts': 0,
            'start_time': None,
            'end_time': None
        }
        
        # 会话管理
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.init_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_session()
        
    async def init_session(self):
        """初始化会话"""
        connector = aiohttp.TCPConnector(
            ssl=False,
            limit=100,
            limit_per_host=30,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(total=30)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        
    async def close_session(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
            
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        return {
            "User-Agent": random.choice(self.user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
            "Referer": "https://www.zhipin.com/"
        }
        
    async def method_aiohttp_advanced(self, url: str) -> str:
        """使用aiohttp高级配置"""
        try:
            logger.info("🌐 使用aiohttp高级方法")
            
            headers = self.get_random_headers()
            
            # 预热访问
            async with self.session.get("https://www.zhipin.com", headers=headers) as response:
                await response.text()
                
            await asyncio.sleep(random.uniform(1, 3))
            
            # 访问目标页面
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    content = await response.text()
                    self.stats['successful_method'] = 'aiohttp-Advanced'
                    return content
                else:
                    logger.warning(f"⚠️ aiohttp响应状态码: {response.status}")
                    return ""
                    
        except Exception as e:
            logger.error(f"❌ aiohttp高级方法失败: {e}")
            return ""
            
    def method_cloudscraper_advanced(self, url: str) -> str:
        """使用CloudScraper绕过Cloudflare"""
        if not AVAILABLE_METHODS['cloudscraper']:
            return ""
            
        try:
            logger.info("☁️ 使用CloudScraper方法")
            
            scraper = cloudscraper.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'desktop': True
                }
            )
            
            headers = self.get_random_headers()
            
            # 预热访问
            scraper.get("https://www.zhipin.com", headers=headers)
            time.sleep(random.uniform(1, 3))
            
            # 访问目标页面
            response = scraper.get(url, headers=headers)
            
            if response.status_code == 200:
                self.stats['successful_method'] = 'CloudScraper-Advanced'
                return response.text
            else:
                logger.warning(f"⚠️ CloudScraper响应状态码: {response.status_code}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ CloudScraper高级方法失败: {e}")
            return ""
            
    def method_seleniumbase_uc(self, url: str) -> str:
        """使用SeleniumBase UC模式"""
        if not AVAILABLE_METHODS['seleniumbase']:
            return ""
            
        try:
            logger.info("🛡️ 使用SeleniumBase UC模式")
            
            with SB(uc=True, test=True, headless=True) as sb:
                # 预热访问
                sb.open("https://www.zhipin.com")
                sb.sleep(random.uniform(1, 3))
                
                # 访问目标页面
                sb.open(url)
                sb.sleep(random.uniform(3, 6))
                
                # 等待页面元素
                try:
                    sb.wait_for_element(".job-list, .job-item, .job-card", timeout=10)
                except:
                    pass
                    
                # 模拟用户行为
                sb.scroll_to_bottom()
                sb.sleep(1)
                sb.scroll_to_top()
                
                content = sb.get_page_source()
                
            self.stats['successful_method'] = 'SeleniumBase-UC'
            return content
            
        except Exception as e:
            logger.error(f"❌ SeleniumBase UC方法失败: {e}")
            return ""
            
    async def method_playwright_advanced(self, url: str) -> str:
        """使用Playwright高级配置"""
        if not AVAILABLE_METHODS['playwright']:
            return ""
            
        try:
            logger.info("🎭 使用Playwright高级方法")
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(
                    headless=True,
                    args=self.chrome_options
                )
                
                context = await browser.new_context(
                    user_agent=random.choice(self.user_agents),
                    viewport={"width": 1920, "height": 1080},
                    locale="zh-CN",
                    timezone_id="Asia/Shanghai"
                )
                
                page = await context.new_page()
                
                # 预热访问
                await page.goto("https://www.zhipin.com", wait_until="domcontentloaded")
                await page.wait_for_timeout(random.randint(1000, 3000))
                
                # 访问目标页面
                await page.goto(url, wait_until="networkidle")
                await page.wait_for_timeout(random.randint(5000, 8000))

                # 等待页面元素 - 更新选择器以匹配BOSS直聘的实际结构
                try:
                    await page.wait_for_selector(".job-list-box, .job-list, .job-item, .job-card, [ka='search-list']", timeout=15000)
                except:
                    # 如果没有找到职位列表，尝试等待更长时间
                    await page.wait_for_timeout(5000)
                    
                # 模拟用户行为 - 修复JavaScript语法
                await page.evaluate("""
                    () => {
                        const delay = () => Math.random() * 1000 + 500;

                        setTimeout(() => {
                            window.scrollTo(0, window.innerHeight / 2);
                        }, delay());

                        setTimeout(() => {
                            window.scrollTo(0, document.body.scrollHeight);
                        }, delay() * 2);

                        setTimeout(() => {
                            window.scrollTo(0, 0);
                        }, delay() * 3);
                    }
                """)
                
                content = await page.content()
                await browser.close()
                
                self.stats['successful_method'] = 'Playwright-Advanced'
                return content
                
        except Exception as e:
            logger.error(f"❌ Playwright高级方法失败: {e}")
            return ""

    async def crawl_with_all_methods(self, url: str) -> str:
        """使用所有可用方法尝试爬取，按成功率排序"""
        logger.info(f"🎯 开始爬取: {url}")

        # 按成功率和先进程度排序的方法列表
        methods = []

        # 添加可用的异步方法
        if AVAILABLE_METHODS['playwright']:
            methods.append(('Playwright-Advanced', self.method_playwright_advanced, True))
        methods.append(('aiohttp-Advanced', self.method_aiohttp_advanced, True))

        # 添加可用的同步方法
        if AVAILABLE_METHODS['seleniumbase']:
            methods.append(('SeleniumBase-UC', self.method_seleniumbase_uc, False))
        if AVAILABLE_METHODS['cloudscraper']:
            methods.append(('CloudScraper-Advanced', self.method_cloudscraper_advanced, False))

        logger.info(f"📋 可用方法数量: {len(methods)}")

        for i, (method_name, method_func, is_async) in enumerate(methods, 1):
            try:
                logger.info(f"🔄 尝试方法 {i}/{len(methods)}: {method_name}")

                if is_async:
                    html_content = await method_func(url)
                else:
                    html_content = method_func(url)

                if html_content and len(html_content) > 5000:
                    # 检查是否包含职位数据
                    if any(keyword in html_content.lower() for keyword in ['job', 'position', '职位', '工作', 'zhipin']):
                        logger.info(f"✅ 方法 {i} ({method_name}) 成功获取页面内容")
                        return html_content
                    else:
                        logger.warning(f"⚠️ 方法 {i} ({method_name}) 获取页面但无职位相关内容")
                else:
                    logger.warning(f"❌ 方法 {i} ({method_name}) 获取页面失败或内容不完整")

            except Exception as e:
                logger.error(f"❌ 方法 {i} ({method_name}) 异常: {str(e)}")
                continue

            # 方法间延迟
            await asyncio.sleep(random.uniform(1, 3))

        logger.error("❌ 所有方法都失败了")
        self.stats['failed_attempts'] += 1
        return ""

    def extract_job_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取职位URL"""
        soup = BeautifulSoup(html_content, 'html.parser')
        job_urls = []

        # BOSS直聘专用选择器策略
        selectors = [
            # BOSS直聘特定选择器
            "a[ka='search-list']",
            "a[href*='/job_detail/']",
            "a[href*='job_detail']",
            ".job-list-box a",
            ".job-list a",
            ".job-item a",
            ".job-card a",
            ".job-primary a",
            ".job-title a",
            ".job-name a",

            # 高级选择器
            "a[ka='search_list_job']",
            "a[data-jobid]",
            "a[data-jid]",
            "[data-href*='job_detail']",
            ".job-info a",
            ".job-content a",

            # 动态生成的选择器
            "[class*='job'] a[href*='detail']",
            "li[class*='job'] a",
            "div[class*='job'] a",

            # React/Vue组件选择器
            "[data-v-*] a[href*='job_detail']",
            ".job-card-wrapper a",
            ".job-item-wrapper a",

            # 通用链接选择器
            "a[href*='zhipin.com/job_detail']"
        ]

        for selector in selectors:
            try:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href') or link.get('data-href')
                    if href and '/job_detail/' in href:
                        full_url = urljoin(self.base_url, href)
                        if self.is_valid_job_url(full_url):
                            job_urls.append(full_url)
            except Exception:
                continue

        # 从JavaScript变量中提取URL
        job_urls.extend(self._extract_urls_from_js(html_content))

        # 从文本中提取URL
        url_patterns = [
            r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9_-]+\.html[^"\s]*',
            r'/job_detail/[a-zA-Z0-9_-]+\.html[^"\s]*',
            r'"job_detail/[a-zA-Z0-9_-]+\.html[^"]*"',
            r"'job_detail/[a-zA-Z0-9_-]+\.html[^']*'",
            r'job_detail/[a-zA-Z0-9_-]+\.html'
        ]

        for pattern in url_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                match = match.strip('"\'')
                if not match.startswith('http'):
                    match = urljoin(self.base_url, match)
                if self.is_valid_job_url(match):
                    job_urls.append(match)

        return list(set(job_urls))

    def _extract_urls_from_js(self, html_content: str) -> List[str]:
        """从JavaScript变量中提取URL"""
        job_urls = []

        # JavaScript变量模式
        js_patterns = [
            r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
            r'window\.g_initialProps\s*=\s*({.+?});',
            r'jobList["\']?\s*:\s*(\[.+?\])',
            r'jobs["\']?\s*:\s*(\[.+?\])',
            r'"jobList"\s*:\s*(\[.+?\])',
            r'"data"\s*:\s*({.*?"job_detail".*?})'
        ]

        for pattern in js_patterns:
            try:
                matches = re.findall(pattern, html_content, re.DOTALL)
                for match in matches:
                    # 在JSON数据中查找job_detail URL
                    job_detail_matches = re.findall(r'/job_detail/[a-zA-Z0-9_-]+\.html', match)
                    for url_match in job_detail_matches:
                        full_url = urljoin(self.base_url, url_match)
                        if self.is_valid_job_url(full_url):
                            job_urls.append(full_url)
            except Exception:
                continue

        return job_urls

    def is_valid_job_url(self, url: str) -> bool:
        """验证是否为有效的职位URL"""
        if not url:
            return False

        patterns = [
            r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9_-]+\.html',
            r'https://www\.zhipin\.com/jobDetail/[a-zA-Z0-9_-]+',
            r'https://www\.zhipin\.com/position/[a-zA-Z0-9_-]+\.html'
        ]

        for pattern in patterns:
            if re.match(pattern, url):
                return True

        return False

    async def crawl_search_page(self, city_code: str = "", keyword: str = "", page: int = 1) -> List[str]:
        """爬取搜索页面"""
        # 构造搜索URL
        params = []
        if keyword:
            params.append(f"query={quote(keyword)}")
        if city_code:
            params.append(f"city={city_code}")
        if page > 1:
            params.append(f"page={page}")

        if params:
            search_url = f"{self.search_url}?" + "&".join(params)
        else:
            search_url = self.search_url

        logger.info(f"🔍 爬取搜索页面: {search_url}")

        # 使用所有方法尝试爬取
        html_content = await self.crawl_with_all_methods(search_url)

        if html_content:
            # 提取职位URL
            job_urls = self.extract_job_urls_from_html(html_content)
            self.stats['pages_crawled'] += 1
            self.stats['urls_found'] += len(job_urls)

            if job_urls:
                logger.info(f"✅ 成功提取 {len(job_urls)} 个职位URL")
                return job_urls
            else:
                logger.warning(f"⚠️ 页面爬取成功但未找到职位URL")
                # 保存调试文件
                debug_file = f"debug_{int(time.time())}.html"
                with open(debug_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                logger.info(f"🔍 调试文件已保存: {debug_file}")

        return []

    async def crawl_multiple_pages(self, city_code: str = "", keyword: str = "", max_pages: int = 3) -> List[str]:
        """爬取多页数据"""
        all_urls = []

        for page in range(1, max_pages + 1):
            logger.info(f"📄 爬取第 {page} 页 (城市: {city_code or '全国'}, 关键词: {keyword or '全部'})")

            page_urls = await self.crawl_search_page(city_code, keyword, page)

            if not page_urls:
                logger.warning(f"⚠️ 第 {page} 页无数据，停止翻页")
                break

            all_urls.extend(page_urls)

            # 检查重复率，判断是否到达最后一页
            if page > 1:
                overlap = len(set(page_urls) & set(all_urls[:-len(page_urls)]))
                if overlap > len(page_urls) * 0.8:
                    logger.warning(f"⚠️ 第 {page} 页重复率过高 ({overlap}/{len(page_urls)})，可能已到最后一页")
                    break

            # 页面间延迟
            await asyncio.sleep(random.uniform(3, 6))

        return all_urls

    async def crawl_all_job_urls(self, cities: List[str] = None, keywords: List[str] = None, max_pages_per_search: int = 2) -> Set[str]:
        """爬取所有职位URL"""
        self.stats['start_time'] = time.time()

        # 使用默认值
        if cities is None:
            from config import MAJOR_CITIES
            cities = MAJOR_CITIES[:3]  # 前3个主要城市
        if keywords is None:
            from config import HIGH_PRIORITY_KEYWORDS
            keywords = HIGH_PRIORITY_KEYWORDS[:3]  # 前3个热门关键词

        logger.info(f"🚀 开始爬取所有职位URL")
        logger.info(f"📊 城市数量: {len(cities)}")
        logger.info(f"📊 关键词数量: {len(keywords)}")
        logger.info(f"📊 可用技术统计:")
        for method, available in AVAILABLE_METHODS.items():
            status = "✅" if available else "❌"
            logger.info(f"   {status} {method}")
        logger.info("=" * 80)

        # 策略1: 城市+关键词组合搜索
        for city in cities:
            city_code = self.city_codes.get(city, "")
            for keyword in keywords:
                logger.info(f"🎯 搜索组合: {city} + {keyword}")
                urls = await self.crawl_multiple_pages(city_code, keyword, max_pages_per_search)
                self.collected_urls.update(urls)
                logger.info(f"📈 当前累计URL数: {len(self.collected_urls)}")

                # 避免过于频繁的请求
                await asyncio.sleep(random.uniform(2, 4))

        # 策略2: 仅城市搜索
        for city in cities:
            city_code = self.city_codes.get(city, "")
            logger.info(f"🎯 城市搜索: {city} (全部职位)")
            urls = await self.crawl_multiple_pages(city_code, "", max_pages_per_search)
            self.collected_urls.update(urls)
            logger.info(f"📈 当前累计URL数: {len(self.collected_urls)}")

            await asyncio.sleep(random.uniform(2, 4))

        # 策略3: 仅关键词搜索（全国范围）
        for keyword in keywords:
            logger.info(f"🎯 关键词搜索: {keyword} (全国)")
            urls = await self.crawl_multiple_pages("", keyword, max_pages_per_search)
            self.collected_urls.update(urls)
            logger.info(f"📈 当前累计URL数: {len(self.collected_urls)}")

            await asyncio.sleep(random.uniform(2, 4))

        self.stats['end_time'] = time.time()
        self.stats['unique_urls'] = len(self.collected_urls)

        return self.collected_urls

    def save_urls_to_file(self, filename: str = None) -> str:
        """保存URL到文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"boss_job_urls_{timestamp}.txt"

        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)

        filepath = os.path.join(output_dir, filename)
        sorted_urls = sorted(list(self.collected_urls))

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# BOSS直聘职位URL - 爬取结果\n")
            f.write(f"# 爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 使用方法: {self.stats['successful_method']}\n")
            f.write(f"# 总计URL数: {len(sorted_urls)}\n")
            f.write(f"# ==========================================\n\n")

            for url in sorted_urls:
                f.write(url + '\n')

        logger.info(f"💾 URL已保存到: {filepath}")
        logger.info(f"📊 总计URL数量: {len(sorted_urls)}")

        return filepath

    def print_statistics(self):
        """打印统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] and self.stats['start_time'] else 0

        print("\n" + "=" * 80)
        print("📊 爬取统计报告")
        print("=" * 80)
        print(f"🏆 成功方法: {self.stats['successful_method']}")
        print(f"⏱️  总耗时: {duration:.2f} 秒")
        print(f"📄 爬取页面: {self.stats['pages_crawled']}")
        print(f"🔗 发现URL总数: {self.stats['urls_found']}")
        print(f"✨ 去重后URL数: {self.stats['unique_urls']}")
        print(f"❌ 失败尝试数: {self.stats['failed_attempts']}")

        if self.stats['pages_crawled'] > 0:
            print(f"📈 平均每页URL数: {self.stats['urls_found'] / self.stats['pages_crawled']:.1f}")

        print(f"\n🔧 技术可用性:")
        for method, available in AVAILABLE_METHODS.items():
            status = "✅ 可用" if available else "❌ 不可用"
            print(f"   {method}: {status}")

        print("=" * 80)
